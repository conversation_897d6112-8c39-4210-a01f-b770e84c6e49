/* QuillEditor 容器样式 */
.editorContainer {
  position: relative;
  transition: all 0.3s ease;
}

/* 全屏模式样式 */
.fullscreen {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 9999 !important;
  background: white;
  padding: 20px;
  box-sizing: border-box;
}

/* 全屏模式下的编辑器样式 */
.fullscreen .editor {
  height: calc(100vh - 40px) !important;
}

.fullscreen .editor .ql-container {
  height: calc(100% - 42px) !important;
}

.fullscreen .editor .ql-editor {
  height: 100% !important;
  overflow-y: auto !important;
}

/* 自定义工具栏样式 */
.customToolbar {
  border: 1px solid #ccc;
  border-bottom: none;
  background: #f8f8f8;
  padding: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

/* 全屏按钮样式 */
.fullscreenButton {
  width: 28px !important;
  height: 28px !important;
  padding: 5px !important;
  border: none !important;
  background: transparent !important;
  cursor: pointer !important;
  border-radius: 3px !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: background-color 0.2s ease !important;
}

.fullscreenButton:hover {
  background-color: #e0e0e0 !important;
}

.fullscreenButton svg {
  width: 18px !important;
  height: 18px !important;
  color: #444 !important;
}

/* 普通模式下的编辑器样式 */
.editor {
  transition: all 0.3s ease;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .fullscreen {
    padding: 10px;
  }

  .fullscreen .editor {
    height: calc(100vh - 20px) !important;
  }
}

/* 确保全屏模式下工具栏可见 */
.fullscreen .customToolbar {
  position: sticky !important;
  top: 0 !important;
  z-index: 1 !important;
  background: #f8f8f8 !important;
  border-bottom: 1px solid #ccc !important;
}

/* 全屏模式下的滚动条样式 */
.fullscreen :global(.ql-editor)::-webkit-scrollbar {
  width: 8px;
}

.fullscreen :global(.ql-editor)::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.fullscreen :global(.ql-editor)::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.fullscreen :global(.ql-editor)::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
