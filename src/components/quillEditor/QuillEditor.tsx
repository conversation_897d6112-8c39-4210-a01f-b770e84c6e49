import React, { useState, useEffect, ReactNode } from 'react';
import dynamic from 'next/dynamic';
import 'react-quill-new/dist/quill.snow.css';
import styles from './QuillEditor.module.css';
import { Expand, Shrink } from 'lucide-react';

const ReactQuill = dynamic(() => import('react-quill-new'), { ssr: false });

// 自定义工具栏组件
interface CustomToolbarProps {
  isFullscreen: boolean;
  onToggleFullscreen: () => void;
  fullscreenIcon?: ReactNode;
  exitFullscreenIcon?: ReactNode;
}

const CustomToolbar: React.FC<CustomToolbarProps> = ({
  isFullscreen,
  onToggleFullscreen,
  fullscreenIcon,
  exitFullscreenIcon,
}) => (
  <div id="toolbar" className={styles.customToolbar}>
    <select className="ql-header" defaultValue="">
      <option value="1">标题 1</option>
      <option value="2">标题 2</option>
      <option value="3">标题 3</option>
      <option value="4">标题 4</option>
      <option value="5">标题 5</option>
      <option value="">正文</option>
    </select>

    <button className="ql-bold" />
    <button className="ql-italic" />
    <button className="ql-underline" />
    <button className="ql-strike" />
    <button className="ql-blockquote" />

    <button className="ql-list" value="ordered" />
    <button className="ql-list" value="bullet" />
    <button className="ql-indent" value="-1" />
    <button className="ql-indent" value="+1" />

    <button className="ql-link" />
    <button className="ql-image" />

    <button className="ql-clean" />

    <button
      className={styles.fullscreenButton}
      onClick={onToggleFullscreen}
      title={isFullscreen ? '退出全屏' : '全屏编辑'}
      type="button"
    >
      {isFullscreen ? exitFullscreenIcon : fullscreenIcon}
    </button>
  </div>
);

interface QuillEditorProps {
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  fullscreenIcon?: ReactNode;
  exitFullscreenIcon?: ReactNode;
  [key: string]: any;
}

function QuillEditor(props: QuillEditorProps) {
  const {
    value: propValue,
    onChange,
    fullscreenIcon,
    exitFullscreenIcon,
    ...restProps
  } = props;

  const [value, setValue] = useState(propValue || '');
  const [isFullscreen, setIsFullscreen] = useState(false);

  // 全屏切换函数
  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  // 使用自定义工具栏配置
  const modulesDefault = {
    toolbar: {
      container: '#toolbar',
      // TODO 图片上传, 插入图片后输入文字导致图片重载，页面闪动。 复制粘贴图片上传cloudinary 还没处理
      // handlers: {
      //   image: async function imageHandler(this: any) {
      //     console.log('this', this);
      //     const input = document.createElement('input');
      //     input.setAttribute('type', 'file');
      //     input.setAttribute('accept', 'image/*');
      //     input.click();

      //     input.onchange = async () => {
      //       const file = input.files?.[0];
      //       if (file) {
      //         const { uploadImgToCloud } = await import('@/lib/cloudinary');

      //         let hideLoading: any;
      //         try {
      //           hideLoading = message.loading('图片上传中...', 0);
      //           const result = await uploadImgToCloud(file);
      //           if (result && result.secure_url) {
      //             const range = this.quill.getSelection();
      //             this.quill.insertEmbed(
      //               range.index,
      //               'image',
      //               result.secure_url
      //             );
      //             this.quill.setSelection(range.index + 1);
      //             hideLoading();

      //             message.success('图片上传成功');
      //           } else {
      //             hideLoading();
      //             message.error('图片上传失败，请重试');
      //           }
      //         } catch (error) {
      //           if (hideLoading) hideLoading();

      //           message.error('图片上传失败，请检查网络连接');
      //         }
      //       }
      //     };
      //   },
      // },
    },
  };

  function handleChange(newValue: string) {
    setValue(newValue);
    onChange?.(newValue);
  }

  // 同步外部value变化
  useEffect(() => {
    if (propValue !== undefined) {
      setValue(propValue);
    }
  }, [propValue]);

  // 处理ESC键退出全屏
  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isFullscreen) {
        setIsFullscreen(false);
      }
    };

    if (isFullscreen) {
      document.addEventListener('keydown', handleEscKey);
      // 防止页面滚动
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.removeEventListener('keydown', handleEscKey);
      document.body.style.overflow = '';
    };
  }, [isFullscreen]);

  return (
    <div
      className={`${styles.editorContainer} ${isFullscreen ? styles.fullscreen : ''}`}
    >
      <CustomToolbar
        isFullscreen={isFullscreen}
        onToggleFullscreen={toggleFullscreen}
        fullscreenIcon={<Expand />}
        exitFullscreenIcon={<Shrink />}
      />
      <ReactQuill
        {...restProps}
        value={value}
        onChange={handleChange}
        modules={restProps.modules || modulesDefault}
        className={styles.editor}
      />
    </div>
  );
}

export default QuillEditor;
